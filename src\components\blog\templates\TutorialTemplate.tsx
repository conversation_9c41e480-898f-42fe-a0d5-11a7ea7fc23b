import Link from 'next/link';
import Image from 'next/image';
import { BlogPost } from '@/lib/supabase';

interface TutorialTemplateProps {
  post: BlogPost;
}

export default function TutorialTemplate({ post }: TutorialTemplateProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Extract headings from content for table of contents
  const extractHeadings = (content: string) => {
    const headingRegex = /<h([2-4])[^>]*>(.*?)<\/h[2-4]>/gi;
    const headings: { level: number; text: string; id: string }[] = [];
    let match;

    while ((match = headingRegex.exec(content)) !== null) {
      const level = parseInt(match[1]);
      const text = match[2].replace(/<[^>]*>/g, ''); // Strip HTML tags
      const id = text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
      headings.push({ level, text, id });
    }

    return headings;
  };

  const headings = extractHeadings(post.content);

  return (
    <div className="max-w-7xl mx-auto">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Main Content */}
        <article className="lg:col-span-3">
          {/* Header */}
          <header className="mb-8">
            {/* Tutorial Badge */}
            <div className="flex items-center mb-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                📚 Tutorial
              </span>
              <span className="mx-3 text-gray-400">•</span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {post.reading_time} min read
              </span>
              <span className="mx-3 text-gray-400">•</span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {post.view_count} views
              </span>
            </div>

            {/* Title */}
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
              {post.title}
            </h1>

            {/* Excerpt */}
            <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-6 leading-relaxed px-4">
              Follow along with this step-by-step tutorial to learn something new. We&apos;ve broken it down into easy-to-follow sections.
            </p>

            {/* Tutorial Info */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mb-8">
              <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">
                📋 What You'll Learn
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-blue-800 dark:text-blue-200 mb-2">
                    <strong>Difficulty:</strong> Beginner to Intermediate
                  </p>
                  <p className="text-sm text-blue-800 dark:text-blue-200 mb-2">
                    <strong>Time Required:</strong> {post.reading_time} minutes
                  </p>
                </div>
                <div>
                  {post.category && (
                    <p className="text-sm text-blue-800 dark:text-blue-200 mb-2">
                      <strong>Category:</strong> {post.category.name}
                    </p>
                  )}
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>Last Updated:</strong> {formatDate(post.updated_at)}
                  </p>
                </div>
              </div>
            </div>

            {/* Author */}
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400 mr-4">
                {post.author?.display_name 
                  ? post.author.display_name.split(' ').map(n => n[0]).join('')
                  : 'A'
                }
              </div>
              <div>
                <p className="font-medium text-gray-900 dark:text-white">
                  {post.author?.display_name || 'Admin'}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Published on {formatDate(post.published_at || post.created_at)}
                </p>
              </div>
            </div>
          </header>

          {/* Featured Image */}
          {post.featured_image && (
            <div className="mb-12">
              <Image
                src={post.featured_image.file_path}
                alt={post.featured_image.alt_text || post.title}
                width={800}
                height={400}
                className="w-full h-64 md:h-96 object-cover rounded-xl shadow-lg"
              />
              {post.featured_image.caption && (
                <p className="text-sm text-gray-500 dark:text-gray-400 text-center mt-2">
                  {post.featured_image.caption}
                </p>
              )}
            </div>
          )}

          {/* Content */}
          <div className="prose prose-lg max-w-none dark:prose-invert prose-blue mb-12">
            <div dangerouslySetInnerHTML={{ __html: post.content }} />
          </div>

          {/* Tutorial Completion */}
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-4">
              🎉 Congratulations!
            </h3>
            <p className="text-green-700 dark:text-green-300 text-sm">
              🎉 Congratulations! You&apos;ve completed this tutorial. 
              Don&apos;t forget to practice what you&apos;ve learned and share your results with the community.
            </p>
            <div className="flex flex-wrap gap-2">
              <button 
                onClick={() => {
                  const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(`Just completed: ${post.title}`)}&url=${encodeURIComponent(window.location.href)}`;
                  window.open(url, '_blank');
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                Share on Twitter
              </button>
              <Link
                href="/blog"
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
              >
                More Tutorials
              </Link>
            </div>
          </div>

          {/* External Resources */}
          {post.external_links && Object.keys(post.external_links).length > 0 && (
            <div className="mb-12 p-6 bg-gray-50 dark:bg-gray-800 rounded-xl">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                📚 Additional Resources
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {post.external_links.github && (
                  <a
                    href={post.external_links.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center p-3 bg-white dark:bg-gray-700 rounded-lg hover:shadow-md transition-shadow"
                  >
                    <span className="mr-3">💻</span>
                    <span className="font-medium">Source Code</span>
                  </a>
                )}
                {post.external_links.demo && (
                  <a
                    href={post.external_links.demo}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center p-3 bg-white dark:bg-gray-700 rounded-lg hover:shadow-md transition-shadow"
                  >
                    <span className="mr-3">🌐</span>
                    <span className="font-medium">Live Demo</span>
                  </a>
                )}
                {post.external_links.youtube && (
                  <a
                    href={post.external_links.youtube}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center p-3 bg-white dark:bg-gray-700 rounded-lg hover:shadow-md transition-shadow"
                  >
                    <span className="mr-3">📺</span>
                    <span className="font-medium">Video Tutorial</span>
                  </a>
                )}
              </div>
            </div>
          )}

          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <div className="pt-8 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag) => (
                  <Link
                    key={tag.id}
                    href={`/blog?tag=${tag.slug}`}
                    className="px-3 py-1 rounded-full text-sm font-medium hover:opacity-80 transition-opacity"
                    style={{ 
                      backgroundColor: tag.color + '20',
                      color: tag.color
                    }}
                  >
                    #{tag.name}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </article>

        {/* Sidebar */}
        <aside className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* Table of Contents */}
            {headings.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  📖 Table of Contents
                </h3>
                <nav className="space-y-2">
                  {headings.map((heading, index) => (
                    <a
                      key={index}
                      href={`#${heading.id}`}
                      className={`block text-sm hover:text-blue-600 dark:hover:text-blue-400 transition-colors ${
                        heading.level === 2 ? 'font-medium' : 
                        heading.level === 3 ? 'ml-4 text-gray-600 dark:text-gray-400' : 
                        'ml-8 text-gray-500 dark:text-gray-500'
                      }`}
                    >
                      {heading.text}
                    </a>
                  ))}
                </nav>
              </div>
            )}

            {/* Tutorial Progress */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                📈 Your Progress
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Reading Progress</span>
                  <span className="font-medium">0%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: '0%' }}></div>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Scroll to track your progress
                </p>
              </div>
            </div>

            {/* Back to Tutorials */}
            <div className="text-center">
              <Link
                href="/blog?category=tutorials"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                ← More Tutorials
              </Link>
            </div>
          </div>
        </aside>
      </div>
    </div>
  );
}
